import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, TextInput } from 'react-native';
import { Edit, Check, Search, X } from 'lucide-react-native';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchUsers, updateLeadAssignment } from '@/lib/api';
import UserAvatar from './UserAvatar';

interface AssignmentDropdownProps {
  leadId: number;
  currentAssignedUser?: {
    id: number;
    name: string;
    profile_image?: string;
  };
  onAssignmentChange?: () => void;
}

export default function AssignmentDropdown({
  leadId,
  currentAssignedUser,
  onAssignmentChange,
}: AssignmentDropdownProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const queryClient = useQueryClient();

  // Fetch users for assignment dropdown
  const { data: users = [] } = useQuery({
    queryKey: ['users'],
    queryFn: fetchUsers,
  });

  // Transform users data for dropdown and ensure current assigned user is included
  const userOptions = useMemo(() => {
    const options = users.map(user => ({
      id: user.id.toString(),
      label: user.name,
      value: user
    }));

    // Always include current assigned user if available
    if (currentAssignedUser) {
      const currentUserExists = options.some(option => option.id === currentAssignedUser.id.toString());

      if (!currentUserExists) {
        options.unshift({
          id: currentAssignedUser.id.toString(),
          label: currentAssignedUser.name,
          value: currentAssignedUser
        });
      }
    }

    // If no users from API and no current user, add a placeholder
    if (options.length === 0) {
      return [{
        id: 'no-agents',
        label: 'No agents available',
        value: null
      }];
    }

    return options;
  }, [users, currentAssignedUser]);

  // Filter options based on search query
  const filteredOptions = useMemo(() => {
    if (!searchQuery.trim()) {
      return userOptions;
    }
    return userOptions.filter(option =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [userOptions, searchQuery]);

  // Assignment mutation with optimistic updates
  const assignmentMutation = useMutation({
    mutationFn: async (userId: number) => {
      return updateLeadAssignment(leadId, userId);
    },
    onMutate: async (userId: number) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['lead', leadId] });

      // Snapshot the previous value
      const previousLead = queryClient.getQueryData(['lead', leadId]);

      // Find the user being assigned
      const assignedUser = users.find(user => user.id === userId);

      // Optimistically update the cache
      queryClient.setQueryData(['lead', leadId], (old: any) => {
        if (!old) return old;

        return {
          ...old,
          latest_assignment: {
            id: Date.now(), // Temporary ID
            user_id: userId,
            lead_id: leadId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            user: assignedUser || {
              id: userId,
              name: `User ${userId}`,
              profile_image: null,
              position: 'Agent'
            }
          }
        };
      });

      // Return a context object with the snapshotted value
      return { previousLead };
    },
    onSuccess: (data) => {
      console.log('Assignment successful:', data);
      // Update with real data if available
      if (data && data.latest_assignment) {
        queryClient.setQueryData(['lead', leadId], (old: any) => {
          if (!old) return old;
          return {
            ...old,
            latest_assignment: data.latest_assignment
          };
        });
      }

      // Invalidate to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      onAssignmentChange?.();
      setIsVisible(false);
      setSearchQuery('');
    },
    onError: (error, userId, context) => {
      console.error('Assignment error:', error);

      // Rollback optimistic update on error
      if (context?.previousLead) {
        queryClient.setQueryData(['lead', leadId], context.previousLead);
      }
    },
  });

  const handleSelect = (option: any) => {
    if (option.id === 'no-agents' || !option.value) {
      return;
    }
    
    const userId = parseInt(option.id);
    assignmentMutation.mutate(userId);
  };

  const handleModalClose = () => {
    setIsVisible(false);
    setSearchQuery('');
  };

  const renderOption = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[
        styles.option,
        item.id === currentAssignedUser?.id?.toString() && styles.optionSelected,
        item.id === 'no-agents' && styles.optionDisabled,
      ]}
      onPress={() => handleSelect(item)}
      disabled={item.id === 'no-agents' || assignmentMutation.isPending}
    >
      <View style={styles.optionContent}>
        {item.value && (
          <UserAvatar
            imageUrl={item.value.profile_image}
            name={item.value.name}
            size={32}
            fontSize={14}
          />
        )}
        <Text style={[
          styles.optionText,
          item.id === currentAssignedUser?.id?.toString() && styles.optionTextSelected,
          item.id === 'no-agents' && styles.optionTextDisabled,
        ]}>
          {item.label}
        </Text>
      </View>
      {item.id === currentAssignedUser?.id?.toString() && (
        <Check size={16} color="#B89C4C" />
      )}
    </TouchableOpacity>
  );

  return (
    <>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setIsVisible(true)}
        disabled={assignmentMutation.isPending}
      >
        <Edit size={18} color="#6B7280" />
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={handleModalClose}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleModalClose}
        >
          <View style={styles.modal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Assign to Agent</Text>
              <TouchableOpacity onPress={handleModalClose} style={styles.closeButton}>
                <X size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.searchContainer}>
              <Search size={16} color="#6B7280" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search agents..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <FlatList
              data={filteredOptions}
              renderItem={renderOption}
              keyExtractor={(item) => item.id.toString()}
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                searchQuery.trim() ? (
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>No agents found</Text>
                  </View>
                ) : null
              }
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  selector: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '100%',
    maxHeight: '70%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  optionsList: {
    maxHeight: 300,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  optionSelected: {
    backgroundColor: '#FEF3C7',
  },
  optionDisabled: {
    opacity: 0.5,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    color: '#111827',
    marginLeft: 12,
  },
  optionTextSelected: {
    color: '#B89C4C',
    fontWeight: '500',
  },
  optionTextDisabled: {
    color: '#9CA3AF',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
  },
});
