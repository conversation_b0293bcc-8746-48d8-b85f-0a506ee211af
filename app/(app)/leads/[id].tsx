import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Linking, Platform, TextInput } from 'react-native';
import { useLocalSearchParams, Stack, router, useFocusEffect } from 'expo-router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, Phone, Mail, MapPin, Building2, Calendar, CircleCheck, Clock, Star, User, Briefcase, Flag, Globe, Info, Hash, FileText, Edit3, Edit, Save, X } from 'lucide-react-native';
import Toast from 'react-native-toast-message';
import { fetchLead, fetchLeadStatuses, api } from '@/lib/api';
import UserAvatar from '@/components/UserAvatar';
import LeadRating from '@/components/LeadRating';
import LeadStatus from '@/components/LeadStatus';
import AssignmentDropdown from '@/components/AssignmentDropdown';

import RatingAlertModal from '@/components/RatingAlertModal';

export default function LeadDetails() {
  const { id, mode, editedLead } = useLocalSearchParams();
  const leadId = typeof id === 'string' ? parseInt(id, 10) : 0;
  const isEditMode = mode === 'edit';
  const queryClient = useQueryClient();

  const [showRatingModal, setShowRatingModal] = useState(false);
  const [currentDisplayStatus, setCurrentDisplayStatus] = useState<any>(null);
  const [currentDisplayRating, setCurrentDisplayRating] = useState<string>('');

  // Requirements editing state
  const [isEditingRequirements, setIsEditingRequirements] = useState(false);
  const [editedRequirements, setEditedRequirements] = useState('');

  // Edit mode state
  const [editForm, setEditForm] = useState({
    contact_id: 0,
    ad_type: 'rent',
    status_id: 11,
    agent_id: 0,
    remarks: '',
    request: {
      propertyTypes: [] as number[],
      operationType: 'rent' as 'rent' | 'sale',
      requirements: '',
      minArea: '',
      maxArea: '',
      minPrice: '',
      maxPrice: '',
      balcony: 0,
      parking: 0,
      isExclusiveOrHotDeal: false,
      pantry: '',
      bedrooms: [] as number[],
      bathrooms: [] as number[],
      marketingPlatforms: [] as number[],
      amenities: [] as number[],
      listingViews: [] as number[],
      kitchen: ''
    }
  });

  // Mutation for updating requirements
  const updateRequirementsMutation = useMutation({
    mutationFn: async (requirements: string) => {
      const response = await api.patch(`/leads/${leadId}`, {
        remarks: requirements
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      setIsEditingRequirements(false);
    },
    onError: (error) => {
      console.error('Error updating requirements:', error);
    },
  });

  // Mutation for updating entire lead (edit mode)
  const updateLeadMutation = useMutation({
    mutationFn: async (leadData: typeof editForm) => {
      const payload = {
        contact_id: leadData.contact_id,
        ad_type: leadData.ad_type,
        status_id: leadData.status_id,
        agent_id: leadData.agent_id,
        remarks: leadData.remarks,
        request: {
          ...leadData.request,
          // Convert boolean values to 0/1
          balcony: leadData.request.balcony ? 1 : 0,
          parking: leadData.request.parking ? 1 : 0,
          isExclusiveOrHotDeal: leadData.request.isExclusiveOrHotDeal
        }
      };

      console.log('Updating lead with payload:', JSON.stringify(payload, null, 2));
      const response = await api.patch(`/leads/${leadId}`, payload);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });

      // Show success message
      console.log('Lead updated successfully:', data);

      // Navigate back or show success toast
      router.back();
    },
    onError: (error) => {
      console.error('Error updating lead:', error);
      // Show error toast/snackbar here
    },
  });

  const { data: lead, isLoading, error } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => fetchLead(leadId),
    enabled: leadId > 0,
  });

  // Populate edit form when lead data is available and in edit mode
  useEffect(() => {
    if (lead && isEditMode) {
      let parsedRequest: any = {};
      try {
        if ((lead as any).leads_request) {
          parsedRequest = JSON.parse((lead as any).leads_request);
        }
      } catch (e) {
        console.error('Error parsing leads_request for edit form:', e);
      }

      setEditForm({
        contact_id: lead.contact?.id || 0,
        ad_type: (lead as any).ad_type || 'rent',
        status_id: lead.lead_status_id || 11,
        agent_id: lead.latest_assignment?.user?.id || 0,
        remarks: (lead as any).remarks || lead.requirements || '',
        request: {
          propertyTypes: parsedRequest.propertyTypes || [],
          operationType: parsedRequest.operationType || (lead as any).ad_type || 'rent',
          requirements: parsedRequest.requirements || lead.requirements || '',
          minArea: parsedRequest.minArea || '',
          maxArea: parsedRequest.maxArea || '',
          minPrice: parsedRequest.minPrice || '',
          maxPrice: parsedRequest.maxPrice || '',
          balcony: parsedRequest.balcony || 0,
          parking: parsedRequest.parking || 0,
          isExclusiveOrHotDeal: parsedRequest.isExclusiveOrHotDeal || false,
          pantry: parsedRequest.pantry || '',
          bedrooms: parsedRequest.bedrooms || [],
          bathrooms: parsedRequest.bathrooms || [],
          marketingPlatforms: parsedRequest.marketingPlatforms || [],
          amenities: parsedRequest.amenities || [],
          listingViews: parsedRequest.listingViews || [],
          kitchen: parsedRequest.kitchen || ''
        }
      });
    }
  }, [lead, isEditMode]);

  // Fetch lead statuses for modal
  const { data: leadStatuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  // Set current display status when lead and leadStatuses are loaded
  React.useEffect(() => {
    if (lead && leadStatuses.length > 0) {
      const statusObj = leadStatuses.find(s => s.id.toString() === lead.lead_status_id?.toString());
      setCurrentDisplayStatus(statusObj);
    }
    if (lead) {
      setCurrentDisplayRating(lead.rating || '');
    }
  }, [lead, leadStatuses]);

  useFocusEffect(
    useCallback(() => {
      console.log('📱 Lead details screen focused, checking for data refresh...');

      const shouldRefresh = queryClient.getQueryState(['lead', leadId])?.isInvalidated;

      if (shouldRefresh) {
        console.log('🔄 Refreshing lead details due to invalidation...');
        queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      }
    }, [queryClient, leadId])
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#B89C4C" />
      </View>
    );
  }

  if (error || !lead) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load lead details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleCall = (phone: string, prefix: string = '') => {
    const fullNumber = `${prefix}${phone}`.replace(/\s+/g, '');
    const phoneUrl = Platform.select({
      ios: `tel:${fullNumber}`,
      android: `tel:${fullNumber}`,
      web: `tel:${fullNumber}`,
    });
    if (phoneUrl) {
      Linking.openURL(phoneUrl).catch(err => console.error('Error opening phone:', err));
    }
  };

  const handleEmail = (email: string) => {
    Linking.openURL(`mailto:${email}`).catch(err => console.error('Error opening email:', err));
  };

  let leadsRequest = null;
  try {
    if (lead.leads_request) {
      leadsRequest = JSON.parse(lead.leads_request);
    }
  } catch (e) {
    console.error('Error parsing leads_request:', e);
  }

  let parsedMetadata = null;
  try {
    if (lead.lead_metadata) {
      parsedMetadata = lead.lead_metadata;
    }
  } catch (e) {
    console.error('Error parsing lead_metadata:', e);
  }

  const getMetadataIcon = (key: string) => {
    switch (key) {
      case 'utm_source':
        return <Globe size={20} color="#4B5563" />;
      case 'utm_campaign':
        return <Flag size={20} color="#4B5563" />;
      case 'gad_source':
        return <Globe size={20} color="#4B5563" />;
      case 'network':
        return <Globe size={20} color="#4B5563" />;
      case 'fbclid':
        return <Globe size={20} color="#4B5563" />;
      case 'form_id':
        return <Info size={20} color="#4B5563" />;
      case 'form_name':
        return <Info size={20} color="#4B5563" />;
      default:
        return <Info size={20} color="#4B5563" />;
    }
  };

  const formatMetadataLabel = (key: string) => {
    return key
      .split('_')
      .map(word => {
        if (word.toLowerCase() === 'utm' || word.toLowerCase() === 'fbclid') {
          return word.toUpperCase();
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
  };

  const formatStatus = (status: string | null) => {
    if (!status) return 'New';
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Sort history in descending order by date
  const sortedHistory = [...lead.operation_history].sort((a, b) =>
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#111827" />
              </TouchableOpacity>
              <View style={styles.headerContent}>
                <Text style={styles.headerTitle}>
                  {isEditMode ? 'Edit Lead' : 'Lead Details'}
                </Text>
                <View style={styles.idBadge}>
                  <Hash size={14} color="#6B7280" />
                  <Text style={styles.idText}>{lead.id}</Text>
                </View>
              </View>
              {isEditMode && (
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={() => updateLeadMutation.mutate(editForm)}
                  disabled={updateLeadMutation.isPending}
                >
                  <Save size={20} color="#fff" />
                  <Text style={styles.saveButtonText}>
                    {updateLeadMutation.isPending ? 'Saving...' : 'Save'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ),
        }}
      />

      <ScrollView style={styles.container}>
        <View style={styles.hero}>
          <UserAvatar
            imageUrl={lead.contact.profile_image}
            name={lead.contact.name}
            size={80}
            fontSize={32}
          />
          <Text style={styles.heroName}>{lead.contact.name}</Text>
          <View style={styles.heroStats}>
            <TouchableOpacity onPress={() => setShowRatingModal(true)}>
              <LeadRating rating={currentDisplayRating} size="large" showLabel showEditIcon />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.statusContainer}
              onPress={() => {
                router.push({
                  pathname: '/leads/edit-status',
                  params: {
                    leadId: leadId.toString(),
                    currentStatus: currentDisplayStatus?.id?.toString() || ''
                  }
                });
              }}
            >
              <LeadStatus
                name={currentDisplayStatus?.name}
                backgroundColor={currentDisplayStatus?.background_color}
                size="large"
                showEditIcon
              />
            </TouchableOpacity>
          </View>
          {lead.inquired_ref_no && (
            <View style={styles.referenceContainer}>
              <FileText size={16} color="#6B7280" />
              <Text style={styles.referenceText}>
                Ref: {lead.inquired_ref_no}
              </Text>
            </View>
          )}
        </View>

        {lead.latest_assignment?.user && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Assignment</Text>
              <AssignmentDropdown
                leadId={leadId}
                currentAssignedUser={lead.latest_assignment.user}
                onAssignmentChange={() => {
                  // Refresh will happen automatically via query invalidation
                }}
              />
            </View>
            <View style={styles.assignmentInfo}>
              <UserAvatar
                imageUrl={lead.latest_assignment.user.profile_image}
                name={lead.latest_assignment.user.name}
                size={48}
                fontSize={18}
              />
              <View style={styles.agentInfo}>
                <Text style={styles.agentName}>{lead.latest_assignment.user.name}</Text>
                <Text style={styles.agentPosition}>{lead.latest_assignment.user.position}</Text>
                <Text style={styles.assignmentDate}>
                  Assigned on {formatDate(lead.latest_assignment.created_at)}
                </Text>
              </View>
            </View>
          </View>
        )}

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => {
                router.push({
                  pathname: '/leads/create/select-contact',
                  params: {
                    leadId: leadId.toString(),
                    mode: 'edit'
                  }
                });
              }}
            >
              <Edit size={18} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.contactSummary}>
            <View style={styles.infoItem}>
              <User size={20} color="#4B5563" />
              <Text style={styles.infoText}>{lead.contact.name}</Text>
            </View>
            {lead.contact.email_1 && (
              <View style={styles.infoItem}>
                <Mail size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.email_1}</Text>
              </View>
            )}
          </View>

          <View style={styles.contactInfo}>
            {lead.contact.company_name && (
              <View style={styles.infoItem}>
                <Briefcase size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.company_name}</Text>
              </View>
            )}

            {lead.contact.mobile_1 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleCall(lead.contact.mobile_1!, lead.contact.prefix_mobile_1)}
              >
                <Phone size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>
                  {lead.contact.prefix_mobile_1} {lead.contact.mobile_1}
                </Text>
              </TouchableOpacity>
            )}

            {lead.contact.mobile_2 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleCall(lead.contact.mobile_2!, lead.contact.prefix_mobile_2)}
              >
                <Phone size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>
                  {lead.contact.prefix_mobile_2} {lead.contact.mobile_2}
                </Text>
              </TouchableOpacity>
            )}

            {lead.contact.email_1 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleEmail(lead.contact.email_1!)}
              >
                <Mail size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>{lead.contact.email_1}</Text>
              </TouchableOpacity>
            )}

            {lead.contact.email_2 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleEmail(lead.contact.email_2!)}
              >
                <Mail size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>{lead.contact.email_2}</Text>
              </TouchableOpacity>
            )}

            {lead.contact.country && (
              <View style={styles.infoItem}>
                <Flag size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.country}</Text>
              </View>
            )}

            {lead.contact.address && (
              <View style={styles.infoItem}>
                <MapPin size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.address}</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lead Source</Text>
          <View style={styles.sourceInfo}>
            <View style={styles.infoItem}>
              <Globe size={20} color="#4B5563" />
              <Text style={styles.infoText}>Platform: {lead.resolved_platform}</Text>
            </View>
          </View>
        </View>

        {parsedMetadata && Object.keys(parsedMetadata).some(key => parsedMetadata[key]) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Lead Metadata</Text>
            <View style={styles.metadataContainer}>
              {Object.entries(parsedMetadata).map(([key, value]) => {
                if (value) {
                  return (
                    <View key={key} style={styles.metadataItem}>
                      <View style={styles.metadataHeader}>
                        {getMetadataIcon(key)}
                        <Text style={styles.metadataLabel}>
                          {formatMetadataLabel(key)}
                        </Text>
                      </View>
                      <Text style={styles.metadataValue}>{value}</Text>
                    </View>
                  );
                }
                return null;
              })}
            </View>
          </View>
        )}

        {leadsRequest && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Request Details</Text>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => {
                  router.push({
                    pathname: '/leads/create/details',
                    params: {
                      leadId: leadId.toString(),
                      mode: 'edit',
                      adType: leadsRequest.ot || 'rent'
                    }
                  });
                }}
              >
                <Edit size={18} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <View style={styles.requestInfo}>
              {leadsRequest.ot && (
                <View style={styles.requestItem}>
                  <Building2 size={20} color="#4B5563" />
                  <Text style={styles.requestText}>
                    Operation Type: {leadsRequest.ot.toUpperCase()}
                  </Text>
                </View>
              )}
              {leadsRequest.be && leadsRequest.be.length > 0 && (
                <View style={styles.requestItem}>
                  <Text style={styles.requestText}>
                    Bedrooms: {(Array.isArray(leadsRequest.be) ? leadsRequest.be : []).join(', ')}
                  </Text>
                </View>
              )}
              {lead.filter_budget_min || lead.filter_budget_max ? (
                <View style={styles.requestItem}>
                  <Text style={styles.requestText}>
                    Budget: {lead.filter_budget_min || 'Any'} - {lead.filter_budget_max || 'Any'}
                  </Text>
                </View>
              ) : null}
            </View>
          </View>
        )}

        {lead.requirements && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Requirements</Text>
              <View style={styles.requirementsActions}>
                {isEditingRequirements && (
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setIsEditingRequirements(false);
                      setEditedRequirements('');
                    }}
                  >
                    <X size={18} color="#EF4444" />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => {
                    if (isEditingRequirements) {
                      // Save requirements
                      updateRequirementsMutation.mutate(editedRequirements);
                    } else {
                      // Start editing
                      setEditedRequirements(lead.requirements || '');
                      setIsEditingRequirements(true);
                    }
                  }}
                >
                  {isEditingRequirements ? (
                    <Save size={18} color="#22C55E" />
                  ) : (
                    <Edit size={18} color="#6B7280" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {isEditingRequirements ? (
              <TextInput
                style={[styles.requirements, styles.requirementsInput]}
                value={editedRequirements}
                onChangeText={setEditedRequirements}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            ) : (
              <Text style={styles.requirements}>{lead.requirements}</Text>
            )}
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>History</Text>
          {sortedHistory.map((history) => (
            <View key={history.id} style={styles.historyItem}>
              <View style={styles.historyHeader}>
                {history.content.includes('status updated') ? (
                  <CircleCheck size={20} color="#10B981" />
                ) : (
                  <Clock size={20} color="#6B7280" />
                )}
                <Text style={styles.historyContent}>{history.content}</Text>
              </View>
              <Text style={styles.historyDate}>
                {formatDate(history.created_at)}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Status editing is now handled by navigation to edit-status screen */}

      {/* Rating Alert Modal */}
      <RatingAlertModal
        visible={showRatingModal}
        onClose={() => setShowRatingModal(false)}
        selectedRating={currentDisplayRating}
        onSelectRating={(ratingId: string) => {
          setCurrentDisplayRating(ratingId);
          setShowRatingModal(false);
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  editButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F9F9F9',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  idBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  idText: {
    fontSize: 13,
    color: '#4B5563',
    fontWeight: '500',
  },
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  hero: {
    backgroundColor: '#fff',
    padding: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  heroName: {
    fontSize: 24,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 12,
  },
  heroStats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusEditButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: '#F3F4F6',
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  badgeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  referenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 8,
  },
  referenceText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  section: {
    padding: 20,
    backgroundColor: '#fff',
    marginTop: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  assignmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  agentPosition: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  assignmentDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  contactSummary: {
    gap: 12,
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  contactInfo: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#4B5563',
    flex: 1,
  },
  linkText: {
    color: '#2563EB',
    textDecorationLine: 'underline',
  },
  sourceInfo: {
    gap: 16,
  },
  metadataContainer: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    overflow: 'hidden',
  },
  metadataItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  metadataHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  metadataLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  metadataValue: {
    fontSize: 16,
    color: '#111827',
    marginLeft: 28,
  },
  requestInfo: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
  requestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  requestText: {
    fontSize: 14,
    color: '#4B5563',
  },
  requirements: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
  },
  requirementsInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    minHeight: 100,
  },
  requirementsActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cancelButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  historyItem: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  historyContent: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  historyDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#B89C4C',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});