import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import Button from '@/components/Button';
import ContactSelector from '@/components/ContactSelector';

type Contact = {
  id: number;
  name: string;
  email_1: string | null;
  prefix_mobile_1: string | null;
  mobile_1: string | null;
  company_name: string | null;
};

export default function SelectContact() {
  const { leadId, mode } = useLocalSearchParams();
  const queryClient = useQueryClient();
  const isEditMode = mode === 'edit' && leadId;

  // Mutation for updating lead contact
  const updateContactMutation = useMutation({
    mutationFn: async (contactId: number) => {
      const response = await api.patch(`/leads/${leadId}`, {
        contact_id: contactId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      router.back();
    },
    onError: (error) => {
      console.error('Error updating lead contact:', error);
    },
  });

  const handleSelectContact = (contact: Contact) => {
    if (isEditMode) {
      // Update existing lead's contact
      updateContactMutation.mutate(contact.id);
    } else {
      // Return to the previous screen with the selected contact for new lead creation
      router.push({
        pathname: '/leads/create',
        params: {
          contact: JSON.stringify(contact)
        }
      });
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          presentation: 'modal',
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>
                {isEditMode ? 'Change Contact' : 'Select Contact'}
              </Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <ContactSelector onSelectContact={handleSelectContact} />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
});